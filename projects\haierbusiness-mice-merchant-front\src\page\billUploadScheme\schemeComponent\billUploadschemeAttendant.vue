<script setup lang="ts">
// 方案互动-服务人员
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { AttendantTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeAttendantsEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e) => e.billUnitPrice && e.billPersonNum && e.billUnitPrice >= 0 && e.billPersonNum >= 0,
  );
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.billUnitPrice * e.billPersonNum;
    });

    emit('schemePriceEmit', { type: 'attendant', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

watch(
  () => props.schemeItem,
  (newObj) => {
    // console.log('%c [ 服务人员 - 完整的 newObj ]', 'font-size:13px; background:pink; color:#bf2c9f;', newObj);

    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.attendants || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      const cacheData = props.schemeCacheItem?.attendants || [];

      // 确保缓存数据也包含所有必要字段
      newSchemeList.value = cacheData.map((e) => ({
        ...e,
        // 确保关键字段存在
        miceDemandAttendantId: e.miceDemandAttendantId,
        miceSchemeAttendantId: e.miceSchemeAttendantId || e.miceDemandAttendantId || Date.now() + Math.random(),
        sourceId: e.sourceId || newObj.sourceId, // 如果单个对象没有sourceId，使用顶层的sourceId
        billPersonNum: e.billPersonNum || e.schemePersonNum,
        billUnitPrice: e.billUnitPrice || e.schemeUnitPrice,
        invoiceTempId: e.invoiceTempId,
        statementTempId: e.statementTempId,
      }));

      // 价格计算
      priceCalcFun();
    } else {
      const demandData = JSON.parse(JSON.stringify(newObj))?.attendants || [];
      newSchemeList.value = demandData.map((e) => {
        // 根据实际数据结构进行映射
        const mappedData = {
          // 基础字段 - 处理已经被处理过的数据
          miceDemandAttendantId: e.miceDemandAttendantId, // 需求服务人员id
          miceSchemeAttendantId: e.miceSchemeAttendantId || e.miceDemandAttendantId || Date.now() + Math.random(), // 方案服务人员id

          // 日期和基本信息
          demandDate: e.demandDate,
          type: e.type,
          duty: e.duty,
          description: e.description,

          // 人数 - 使用现有数据
          schemePersonNum: e.schemePersonNum || e.personNum, // 兼容不同字段名
          billPersonNum: e.billPersonNum || e.schemePersonNum || e.personNum, // 优先使用已有的billPersonNum

          // 价格相关 - 使用现有数据
          schemeUnitPrice: e.schemeUnitPrice || null,
          billUnitPrice: e.billUnitPrice || e.schemeUnitPrice || null, // 优先使用已有的billUnitPrice

          // 其他字段 - 处理丢失的字段
          sourceId: e.sourceId || newObj.sourceId, // 如果单个对象没有sourceId，使用顶层的sourceId
          invoiceTempId: e.invoiceTempId, // 保持现有值
          statementTempId: e.statementTempId, // 保持现有值
        };

        console.log('%c [ 服务人员 - 映射后的数据 ]', 'font-size:13px; background:green; color:#fff;', mappedData);
        return mappedData;
      });
      console.log(
        '%c [ 服务人员 - 转换后的数据 ]-30',
        'font-size:13px; background:purple; color:#fff;',
        newSchemeList.value,
      );
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['人员类型', '人数', '工作范围', '备注'];

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const attendantTempSave = () => {
  emit('schemeAttendantsEmit', {
    schemeAttendants: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const attendantSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (!e.billUnitPrice) {
      message.error('请输入' + e.demandDate + '服务人员' + (i + 1) + '账单单价');

      isVerPassed = false;
      anchorJump('schemeAttendantId' + e.demandDate + i);
      return;
    }

    if (!e.billPersonNum || e.billPersonNum <= 0) {
      message.error('请输入' + e.demandDate + '服务人员' + (i + 1) + '账单人数');

      isVerPassed = false;
      anchorJump('schemeAttendantId' + e.demandDate + i);
      return;
    }
  });

  if (isVerPassed) {
    attendantTempSave();
  }

  return isVerPassed;
};

defineExpose({ attendantSub, attendantTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 服务人员 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>服务人员需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '服务人员' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ AttendantTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ AttendantTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.duty || '-' }}
                </template>
                {{ item.duty || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>

          <!-- 左侧添加单价和总额展示 -->
          <div class="scheme_plan_list3 pr12" v-if="item.schemeUnitPrice">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeUnitPrice || '-' }}
              </div>
            </div>
            <div class="scheme_plan_price mt16">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>服务人员方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          {{ item }}
          <div class="scheme_plan_index">
            {{ '服务人员' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ AttendantTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ AttendantTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billPersonNum">
                    {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billPersonNum"
                    @change="changePrice(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="人数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.duty || '-' }}
                </template>
                {{ item.duty || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>

          <div class="scheme_plan_list3 pr12" :id="'schemeAttendantId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView'"
              >
                {{ item.schemeUnitPrice }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.billUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.billUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt16">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.billUnitPrice && item.billPersonNum
                    ? formatNumberThousands(item.billUnitPrice * item.billPersonNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billUnitPrice && item.billPersonNum">
                {{ (item.billPersonNum || 0) + '人*' + item.billUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_attendant.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 价格输入框样式
  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  // 人数输入框样式
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_border {
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    min-height: 32px;
    display: flex;
    align-items: center;
    background-color: #f7f8fa;
  }

  .error_tip {
    border-color: #ff4d4f !important;
    background-color: #fff2f0 !important;
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
