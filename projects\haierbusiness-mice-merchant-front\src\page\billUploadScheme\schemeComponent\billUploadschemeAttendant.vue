测试系统
billUploadschemeInteract.vue:1109  [ record ]-606 {miceId: 311, schemeType: 'billUpload'}
EManange.vue:167 development
billUploadschemeInteract.vue:154  [ 缓存查询 ]-171114 {miceId: 311, sourceId: 1039, mainCode: 'AGENCY20250717135446070221', miceSchemeId: 1060, billTotalPrice: '26065.00', …}
billUploadschemeAttendant.vue:65  [ 服务人员 - 完整的 newObj ] Proxy(Object) {stays: Array(2), places: Array(1), caterings: Array(1), vehicles: Array(1), attendants: <PERSON>rray(1), …}
billUploadschemeAttendant.vue:66  [ 服务人员 - newObj.attendants ] Proxy(Array) {0: Proxy(Object)}
billUploadschemeActivity.vue:61  [ billUploadschemeActivity - 拓展方案数据调试 ] Proxy(Object) {stays: Array(2), places: Array(1), caterings: Array(1), vehicles: Array(1), attendants: <PERSON><PERSON><PERSON>(1), …}
billUploadschemeAttendant.vue:65  [ 服务人员 - 完整的 newObj ] Proxy(Object) {stays: Array(2), places: Array(1), caterings: Array(1), vehicles: Array(1), attendants: Array(1), …}
billUploadschemeAttendant.vue:66  [ 服务人员 - newObj.attendants ] Proxy(Array) {0: Proxy(Object)}
billUploadschemeInteract.vue:573  [ 合计----数据 ]-226 {miceId: 311, sourceId: 1039, mainCode: 'AGENCY20250717135446070221', miceSchemeId: 1060, billTotalPrice: '26065.00', …}